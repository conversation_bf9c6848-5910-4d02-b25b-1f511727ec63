package me.miguel19877.dev.commands;

import me.miguel19877.dev.managers.AchievementManager;
import me.miguel19877.dev.managers.LanguageManager;
import me.miguel19877.dev.utils.Achievement;
import me.miguel19877.dev.utils.AchievementPlayerData;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.Map;

/**
 * Command to view achievements
 */
public class Achievements implements CommandExecutor {
    
    private final AchievementManager achievementManager = AchievementManager.getInstance();
    private final LanguageManager languageManager = LanguageManager.getInstance();
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§cThis command can only be used by players.");
            return true;
        }
        
        Player player = (Player) sender;
        
        // Check if player has achievement data loaded
        AchievementPlayerData playerData = achievementManager.getPlayerData(player.getUniqueId());
        if (playerData == null) {
            String message = languageManager.getMessage(player, "achievement.not_loaded", "");
            if (message == null) {
                message = "§cAchievement data not loaded yet. Please try again in a moment.";
            }
            player.sendMessage(message);
            return true;
        }
        
        Map<String, Achievement> achievements = achievementManager.getAllAchievements();
        if (achievements.isEmpty()) {
            String message = languageManager.getMessage(player, "achievement.none_available", "");
            if (message == null) {
                message = "§cNo achievements available.";
            }
            player.sendMessage(message);
            return true;
        }
        
        // Show achievement list
        showAchievementList(player, achievements, playerData);
        
        return true;
    }
    
    private void showAchievementList(Player player, Map<String, Achievement> achievements, AchievementPlayerData playerData) {
        // Header
        String header = languageManager.getMessage(player, "achievement.list_header", "");
        if (header == null) {
            header = "§6§l=== YOUR ACHIEVEMENTS ===";
        }
        player.sendMessage(header);
        
        int completed = 0;
        int total = achievements.size();
        
        // Group achievements by category
        Map<String, java.util.List<Achievement>> categorizedAchievements = new java.util.HashMap<>();
        for (Achievement achievement : achievements.values()) {
            String category = achievement.getCategory() != null ? achievement.getCategory() : "general";
            categorizedAchievements.computeIfAbsent(category, k -> new java.util.ArrayList<>()).add(achievement);
        }
        
        // Display achievements by category
        for (Map.Entry<String, java.util.List<Achievement>> entry : categorizedAchievements.entrySet()) {
            String category = entry.getKey();
            java.util.List<Achievement> categoryAchievements = entry.getValue();
            
            // Category header
            String categoryName = getCategoryDisplayName(category);
            player.sendMessage("§e§l" + categoryName.toUpperCase() + ":");
            
            for (Achievement achievement : categoryAchievements) {
                boolean isCompleted = playerData.isCompleted(achievement.getId());
                int progress = playerData.getProgress(achievement.getId());
                
                if (isCompleted) {
                    completed++;
                    // Completed achievement
                    player.sendMessage("§a✓ §f" + achievement.getName() + " §7- §aCompleted!");
                } else {
                    // In-progress achievement
                    String progressText = progress + "/" + achievement.getThreshold();
                    double percentage = (double) progress / achievement.getThreshold() * 100;
                    player.sendMessage("§7○ §f" + achievement.getName() + " §7- §e" + progressText + " §7(" + String.format("%.1f", percentage) + "%)");
                }
                
                // Show description
                if (achievement.getDescription() != null && !achievement.getDescription().isEmpty()) {
                    for (String descLine : achievement.getDescription()) {
                        player.sendMessage("  §7" + descLine);
                    }
                }
            }
            player.sendMessage(""); // Empty line between categories
        }
        
        // Footer with statistics
        String footer = languageManager.getMessage(player, "achievement.list_footer", completed, total);
        if (footer == null) {
            footer = "§6Completed: §a" + completed + "§6/§e" + total + " §6achievements";
        }
        player.sendMessage(footer);
    }
    
    private String getCategoryDisplayName(String category) {
        switch (category.toLowerCase()) {
            case "bronze":
                return "Bronze";
            case "silver":
                return "Silver";
            case "gold":
                return "Gold";
            case "diamond":
                return "Diamond";
            case "special":
                return "Special";
            default:
                return "General";
        }
    }
}
