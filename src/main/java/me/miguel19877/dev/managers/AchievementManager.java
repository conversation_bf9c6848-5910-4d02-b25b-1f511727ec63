package me.miguel19877.dev.managers;

import com.google.gson.Gson;
import me.miguel19877.dev.Rankup;
import me.miguel19877.dev.utils.Achievement;
import me.miguel19877.dev.utils.AchievementPlayerData;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Manages achievement system on the Spigot side
 * Handles caching, progress tracking, and communication with Velocity
 */
public class AchievementManager {
    
    private static AchievementManager instance;
    private final Gson gson = new Gson();
    
    // Cache for achievement definitions (received from Velocity)
    private final Map<String, Achievement> achievements = new ConcurrentHashMap<>();
    
    // Cache for player achievement data
    private final Map<UUID, AchievementPlayerData> playerAchievementData = new ConcurrentHashMap<>();
    
    // Achievement channel name
    public static final String ACHIEVEMENT_CHANNEL = "ach:main";
    
    private AchievementManager() {
        // Private constructor for singleton
    }
    
    public static AchievementManager getInstance() {
        if (instance == null) {
            instance = new AchievementManager();
        }
        return instance;
    }
    
    /**
     * Initialize achievement data for a player (called when INIT message is received)
     */
    public void initializePlayerData(UUID playerId, Map<String, Achievement> achievementDefinitions, AchievementPlayerData playerData) {
        // Store achievement definitions
        achievements.putAll(achievementDefinitions);
        
        // Store player data
        playerAchievementData.put(playerId, playerData);
        
        Bukkit.getLogger().info("Initialized achievement data for player " + playerId + 
                " with " + achievementDefinitions.size() + " achievements");
    }
    
    /**
     * Update progress for a specific achievement
     */
    public void updateProgress(Player player, String achievementId, int newProgress) {
        UUID playerId = player.getUniqueId();
        AchievementPlayerData playerData = playerAchievementData.get(playerId);
        
        if (playerData == null) {
            Bukkit.getLogger().warning("No achievement data found for player " + player.getName());
            return;
        }
        
        Achievement achievement = achievements.get(achievementId);
        if (achievement == null) {
            Bukkit.getLogger().warning("Unknown achievement: " + achievementId);
            return;
        }
        
        int oldProgress = playerData.getProgress(achievementId);
        boolean wasCompleted = playerData.isCompleted(achievementId);
        
        // Update progress locally
        playerData.setProgress(achievementId, newProgress);
        
        // Check if achievement was just completed
        if (!wasCompleted && newProgress >= achievement.getThreshold()) {
            handleAchievementCompletion(player, achievement);
            playerData.setCompleted(achievementId, System.currentTimeMillis());
        }
        
        // Send update to Velocity (only if progress actually changed)
        if (oldProgress != newProgress) {
            sendProgressUpdate(player, achievementId, newProgress);
        }
    }
    
    /**
     * Handle achievement completion (execute rewards, show messages)
     */
    private void handleAchievementCompletion(Player player, Achievement achievement) {
        // Show completion message
        String completionMessage = LanguageManager.getInstance().getMessage(player, "achievement.completed", 
                achievement.getName());
        if (completionMessage == null) {
            completionMessage = "§a§lACHIEVEMENT UNLOCKED: §e" + achievement.getName();
        }
        player.sendMessage(completionMessage);
        
        // Execute reward commands
        if (achievement.getCommands() != null) {
            for (String command : achievement.getCommands()) {
                String processedCommand = command.replace("%player%", player.getName());
                Bukkit.getScheduler().runTask(Rankup.getInstance(), () -> {
                    Bukkit.dispatchCommand(Bukkit.getConsoleSender(), processedCommand);
                });
            }
        }
        
        Bukkit.getLogger().info("Player " + player.getName() + " completed achievement: " + achievement.getId());

        // Check if player completed all achievements for lenda_absoluta
        checkLegendaAbsoluta(player);
    }
    
    /**
     * Send progress update to Velocity
     */
    private void sendProgressUpdate(Player player, String achievementId, int progress) {
        Map<String, Object> updateData = new HashMap<>();
        updateData.put("playerId", player.getUniqueId().toString());
        updateData.put("achievementId", achievementId);
        updateData.put("progress", progress);
        
        String json = gson.toJson(updateData);
        byte[] data = json.getBytes(StandardCharsets.UTF_8);
        
        player.sendPluginMessage(Rankup.getInstance(), ACHIEVEMENT_CHANNEL, data);
    }
    
    /**
     * Get achievement definition by ID
     */
    public Achievement getAchievement(String achievementId) {
        return achievements.get(achievementId);
    }
    
    /**
     * Get player achievement data
     */
    public AchievementPlayerData getPlayerData(UUID playerId) {
        return playerAchievementData.get(playerId);
    }
    
    /**
     * Get all cached achievements
     */
    public Map<String, Achievement> getAllAchievements() {
        return new HashMap<>(achievements);
    }
    
    /**
     * Clean up player data when they leave
     */
    public void cleanupPlayerData(UUID playerId) {
        playerAchievementData.remove(playerId);
    }
    
    /**
     * Check if player has completed an achievement
     */
    public boolean hasCompleted(UUID playerId, String achievementId) {
        AchievementPlayerData playerData = playerAchievementData.get(playerId);
        return playerData != null && playerData.isCompleted(achievementId);
    }
    
    /**
     * Get player's progress for an achievement
     */
    public int getProgress(UUID playerId, String achievementId) {
        AchievementPlayerData playerData = playerAchievementData.get(playerId);
        return playerData != null ? playerData.getProgress(achievementId) : 0;
    }
}
