package me.miguel19877.dev.utils;

import java.util.List;

/**
 * Represents an achievement definition received from Velocity
 */
public class Achievement {
    private String id;
    private String name;
    private List<String> description;
    private String icon;
    private int threshold;
    private String category;
    private List<String> commands;

    public Achievement() {
        // Default constructor for Gson
    }

    public Achievement(String id, String name, List<String> description, String icon, int threshold, String category, List<String> commands) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.icon = icon;
        this.threshold = threshold;
        this.category = category;
        this.commands = commands;
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<String> getDescription() {
        return description;
    }

    public void setDescription(List<String> description) {
        this.description = description;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public int getThreshold() {
        return threshold;
    }

    public void setThreshold(int threshold) {
        this.threshold = threshold;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public List<String> getCommands() {
        return commands;
    }

    public void setCommands(List<String> commands) {
        this.commands = commands;
    }

    @Override
    public String toString() {
        return "Achievement{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", description=" + description +
                ", icon='" + icon + '\'' +
                ", threshold=" + threshold +
                ", category='" + category + '\'' +
                ", commands=" + commands +
                '}';
    }
}
