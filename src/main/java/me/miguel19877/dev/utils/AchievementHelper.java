package me.miguel19877.dev.utils;

import me.miguel19877.dev.managers.AchievementManager;
import org.bukkit.entity.Player;

/**
 * Utility class to help with achievement progress tracking
 * This provides convenient methods for updating achievement progress
 */
public class AchievementHelper {
    
    private static final AchievementManager achievementManager = AchievementManager.getInstance();
    
    /**
     * Increment achievement progress by 1
     */
    public static void incrementProgress(Player player, String achievementId) {
        incrementProgress(player, achievementId, 1);
    }
    
    /**
     * Increment achievement progress by specified amount
     */
    public static void incrementProgress(Player player, String achievementId, int amount) {
        if (player == null || achievementId == null) {
            return;
        }
        
        int currentProgress = achievementManager.getProgress(player.getUniqueId(), achievementId);
        int newProgress = currentProgress + amount;
        
        achievementManager.updateProgress(player, achievementId, newProgress);
    }
    
    /**
     * Set achievement progress to specific value
     */
    public static void setProgress(Player player, String achievementId, int progress) {
        if (player == null || achievementId == null) {
            return;
        }
        
        achievementManager.updateProgress(player, achievementId, progress);
    }
    
    /**
     * Check if player has completed an achievement
     */
    public static boolean hasCompleted(Player player, String achievementId) {
        if (player == null || achievementId == null) {
            return false;
        }
        
        return achievementManager.hasCompleted(player.getUniqueId(), achievementId);
    }
    
    /**
     * Get current progress for an achievement
     */
    public static int getProgress(Player player, String achievementId) {
        if (player == null || achievementId == null) {
            return 0;
        }
        
        return achievementManager.getProgress(player.getUniqueId(), achievementId);
    }
    
    /**
     * Get achievement definition
     */
    public static Achievement getAchievement(String achievementId) {
        return achievementManager.getAchievement(achievementId);
    }
}
