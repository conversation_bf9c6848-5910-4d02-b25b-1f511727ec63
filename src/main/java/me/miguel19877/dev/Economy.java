package me.miguel19877.dev;

import me.miguel19877.dev.listeners.AchievementRankListener;
import me.miguel19877.dev.managers.RedisManager;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Tuple;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

public class Economy {
    // In-memory cache
    private static final Map<UUID, Long> coinsCache = new HashMap<>();
    private static final Map<UUID, Long> moneyCache = new HashMap<>();
    private static final Map<UUID, Long> almasCache = new HashMap<>();

    // Redis key prefixes
    private static final String COINS_KEY = "coins:";
    private static final String MONEY_KEY = "money";
    private static final String ALMAS_KEY = "almas:";

    // Utility method to get Jedis connection
    private static Jedis getJedis() {
        return RedisManager.getJedis();
    }

    // Load player data from Redis to cache
    public static void loadPlayer(Player player) {
        UUID playerId = player.getUniqueId();
        try (Jedis jedis = getJedis()) {
            moneyCache.put(playerId, parseLong(jedis.zscore(MONEY_KEY, playerId.toString())));
        }
    }

    // Helper method to parse long safely from Redis data
    private static Long parseLong(Double value) {
        return value != null ? value.longValue() : 0L;
    }

    private static Long parseLong(String value) {
        return value != null ? Long.parseLong(value) : 0L;
    }

    // Save player data from cache to Redis
    public static void savePlayer(Player player) {
        UUID playerId = player.getUniqueId();
        try (Jedis jedis = getJedis()) {
            jedis.zadd(MONEY_KEY, moneyCache.get(playerId), playerId.toString());
        }
    }

    // Get and set methods that interact with the cache
    public static Long getCoins(UUID playerId) {
        return coinsCache.getOrDefault(playerId, 0L);
    }

    public static void setCoins(UUID playerId, Long coins) {
        coinsCache.put(playerId, coins);
    }

    public static Long getMoney(UUID playerId) {
        return moneyCache.getOrDefault(playerId, 0L);
    }

    public static void setMoney(UUID playerId, Long money) {
        moneyCache.put(playerId, money);
        Rankup.getInstance().deltaManager.recordMoneyChange(playerId, money);
    }

    public static Long getAlmas(UUID playerId) {
        return almasCache.getOrDefault(playerId, 0L);
    }

    public static void setAlmas(UUID playerId, Long almas) {
        almasCache.put(playerId, almas);
        // Optionally save to Redis immediately or defer to a batch save
    }

    // Adjust add and remove methods as necessary
    public static void transferCoins(UUID senderId, UUID recipientId, Long amount) {
        Player recipient = Bukkit.getPlayer(recipientId);
        if (recipient != null && recipient.isOnline()) {
            // Both sender and recipient are online
            addCoins(recipientId, amount);
        } else {
            // Recipient is offline, update directly in Redis
            try (Jedis jedis = getJedis()) {
                jedis.incrBy(COINS_KEY + recipientId, amount);
            }
        }
        // Deduct from the sender
        removeCoins(senderId, amount);
    }

    public static void addCoins(UUID playerId, Long amount) {
        if (Bukkit.getPlayer(playerId) != null) {
            coinsCache.put(playerId, getCoins(playerId) + amount);
        } else {
            // Player is offline, update directly in Redis
            try (Jedis jedis = getJedis()) {
                jedis.incrBy(COINS_KEY + playerId, amount);
            }
        }
    }

    public static void removeCoins(UUID playerId, Long amount) {
        if (Bukkit.getPlayer(playerId) != null) {
            Long newAmount = Math.max(0, getCoins(playerId) - amount);
            coinsCache.put(playerId, newAmount);
        } else {
            // Player is offline, update directly in Redis
            try (Jedis jedis = getJedis()) {
                long currentAmount = jedis.decrBy(COINS_KEY + playerId, amount);
                if (currentAmount < 0) {
                    // Ensure the balance does not go negative
                    jedis.set(COINS_KEY + playerId, "0");
                }
            }
        }
    }

    public static void transferMoney(UUID senderId, UUID recipientId, Long amount) {
        Player recipient = Bukkit.getPlayer(recipientId);
        if (recipient != null && recipient.isOnline()) {
            // Both sender and recipient are online
            addMoney(recipientId, amount);
        } else {
            // Recipient is offline, update directly in Redis
            try (Jedis jedis = getJedis()) {
                jedis.zincrby(MONEY_KEY, amount, recipientId.toString());
            }
        }
        // Deduct from the sender
        removeMoney(senderId, amount);
    }

    public static void addMoney(UUID playerId, Long amount) {
        long newamount = getMoney(playerId) + amount;
        if (Bukkit.getPlayer(playerId) != null) {
            moneyCache.put(playerId, newamount);
        } else {
            // Player is offline, update directly in Redis
            try (Jedis jedis = getJedis()) {
                jedis.zincrby(MONEY_KEY, amount, playerId.toString());
            }
        }
        Rankup.getInstance().deltaManager.recordMoneyChange(playerId, newamount);
    }

    public static void removeMoney(UUID playerId, Long amount) {
        long newamount = getMoney(playerId) - amount;
        if (Bukkit.getPlayer(playerId) != null) {
            long newAmount = Math.max(0, newamount);
            moneyCache.put(playerId, newAmount);
        } else {
            // Player is offline, update directly in Redis
            try (Jedis jedis = getJedis()) {
                double currentAmount = jedis.zincrby(MONEY_KEY, -amount, playerId.toString());
                if (currentAmount < 0) {
                    // Ensure the balance does not go negative
                    jedis.zadd(MONEY_KEY, 0, playerId.toString());
                }
            }
        }
        Rankup.getInstance().deltaManager.recordMoneyChange(playerId, newamount);
    }

    // Get Top 5 players money on redis
    public static Set<Tuple> getTop5Money() {
        try (Jedis jedis = getJedis()) {
            return jedis.zrevrangeWithScores(MONEY_KEY, 0, 4);
        }
    }
}
