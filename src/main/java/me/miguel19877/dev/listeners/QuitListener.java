package me.miguel19877.dev.listeners;

import me.miguel19877.dev.managers.AchievementManager;
import me.miguel19877.dev.managers.ClanManager;
import me.miguel19877.dev.managers.LanguageManager;
import me.miguel19877.dev.managers.StreamRewardManager;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerQuitEvent;

import java.util.HashMap;

public class QuitListener implements Listener {

        @EventHandler
        public void onQuit(PlayerQuitEvent event) {
            Player p = event.getPlayer();
            String quitMessage = LanguageManager.getInstance().getMessage(p, "quit.message", p.getName());
            event.setQuitMessage(quitMessage);
            ClanManager.savePlayer(p);

            // Clean up stream reward tracking
            StreamRewardManager.onPlayerLeave(p);

            // Clean up achievement data
            AchievementManager.getInstance().cleanupPlayerData(p.getUniqueId());
            PlaytimeManager.getInstance().savePlayerData(p);
            AchievementShopManager.getInstance().cleanupPlayer(p.getUniqueId());
            AchievementSpecialManager.getInstance().cleanupPlayer(p.getUniqueId());
        }

}
