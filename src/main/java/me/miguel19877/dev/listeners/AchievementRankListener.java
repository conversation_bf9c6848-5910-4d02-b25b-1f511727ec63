package me.miguel19877.dev.listeners;

import me.miguel19877.dev.Economy;
import me.miguel19877.dev.RankSystem;
import me.miguel19877.dev.managers.PrestigeManager;
import me.miguel19877.dev.utils.AchievementHelper;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.Bukkit;

/**
 * Listener for tracking rank, prestige, and money-related achievements
 */
public class AchievementRankListener implements Listener {
    
    /**
     * Check rank-related achievements for a player
     * This should be called whenever a player's rank changes
     */
    public static void checkRankAchievements(Player player) {
        int rankId = RankSystem.getRankId(player);
        
        // minerador_junior - Reach rank Aprendiz (rank 4)
        if (rankId >= 4) {
            AchievementHelper.setProgress(player, "minerador_junior", 1);
        }
        
        // subterraneo_serio - Reach rank Sub-Chefe (rank 11)
        if (rankId >= 11) {
            AchievementHelper.setProgress(player, "subterraneo_serio", 1);
        }
        
        // rei_da_mina - Reach last rank without prestige (rank 22 = Deus)
        if (rankId >= 22 && PrestigeManager.getPrestigeLevel(player) == 0) {
            AchievementHelper.setProgress(player, "rei_da_mina", 1);
        }
    }
    
    /**
     * Check prestige-related achievements for a player
     * This should be called whenever a player's prestige changes
     */
    public static void checkPrestigeAchievements(Player player) {
        int prestigeLevel = PrestigeManager.getPrestigeLevel(player);
        
        // lenda_da_mina - Reach Prestige 1
        if (prestigeLevel >= 1) {
            AchievementHelper.setProgress(player, "lenda_da_mina", 1);
        }
        
        // deus_da_mina - Reach Prestige 10
        if (prestigeLevel >= 10) {
            AchievementHelper.setProgress(player, "deus_da_mina", prestigeLevel);
        }
        
        // pisaste_cada_pedra - Visit all mines up to Prestige 1
        if (prestigeLevel >= 1) {
            // This achievement requires visiting all mines, which would need additional tracking
            // For now, we'll assume if they reached prestige 1, they visited all mines
            AchievementHelper.setProgress(player, "pisaste_cada_pedra", 1);
        }
    }
    
    /**
     * Check money-related achievements for a player
     * This should be called whenever a player's money changes significantly
     */
    public static void checkMoneyAchievements(Player player) {
        long money = Economy.getMoney(player.getUniqueId());
        
        // pe_de_meia - Reach 1,000,000 money
        if (money >= 1000000) {
            AchievementHelper.setProgress(player, "pe_de_meia", 1);
        }
        
        // carteira_pesada - Reach 1,000,000,000 money
        if (money >= 1000000000L) {
            AchievementHelper.setProgress(player, "carteira_pesada", 1);
        }
        
        // milionario_a_vista - Reach 450,000,000,000 money
        if (money >= 450000000000L) {
            AchievementHelper.setProgress(player, "milionario_a_vista", 1);
        }
        
        // milionario_oficial - Reach 1,000,000,000,000 money
        if (money >= 1000000000000L) {
            AchievementHelper.setProgress(player, "milionario_oficial", 1);
        }
    }
    
    /**
     * Periodic task to check money achievements for all online players
     * This runs every 5 minutes to avoid constant checking
     */
    public static void startPeriodicMoneyCheck() {
        new BukkitRunnable() {
            @Override
            public void run() {
                for (Player player : Bukkit.getOnlinePlayers()) {
                    checkMoneyAchievements(player);
                }
            }
        }.runTaskTimerAsynchronously(Bukkit.getPluginManager().getPlugin("CAH"), 0L, 6000L); // Every 5 minutes
    }
}
