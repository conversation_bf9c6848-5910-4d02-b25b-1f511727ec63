package me.miguel19877.dev.listeners;

import me.miguel19877.dev.Economy;
import me.miguel19877.dev.RankSystem;
import me.miguel19877.dev.managers.*;
import me.miguel19877.dev.utils.AchievementHelper;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.bukkit.event.inventory.CraftItemEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.inventory.ItemStack;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Main listener for tracking achievement progress
 */
public class AchievementTrackingListener implements Listener {
    
    // Track first-time events per player session
    private final Map<UUID, Boolean> firstJoinThisSession = new HashMap<>();
    private final Map<UUID, Boolean> hasMinedThisSession = new HashMap<>();
    private final Map<UUID, Boolean> hasCraftedPickaxeThisSession = new HashMap<>();
    
    // Track coal blocks mined
    private final Map<UUID, Integer> coalBlocksMinedThisSession = new HashMap<>();
    
    // Track diamond collection
    private final Map<UUID, Integer> diamondsCollectedThisSession = new HashMap<>();
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        UUID playerId = player.getUniqueId();
        
        // Reset session tracking
        firstJoinThisSession.put(playerId, true);
        hasMinedThisSession.put(playerId, false);
        hasCraftedPickaxeThisSession.put(playerId, false);
        coalBlocksMinedThisSession.put(playerId, 0);
        diamondsCollectedThisSession.put(playerId, 0);
        
        // Track first join achievement
        if (!player.hasPlayedBefore()) {
            // This is handled by playtime tracking - inicio_de_jornada will be tracked by playtime
        }
        
        // Check clan join achievement
        if (ClanManager.isPlayerInClan(playerId)) {
            AchievementHelper.setProgress(player, "recruta_de_clan", 1);
        }
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onBlockBreak(BlockBreakEvent event) {
        if (event.isCancelled()) return;
        
        Player player = event.getPlayer();
        UUID playerId = player.getUniqueId();
        Material blockType = event.getBlock().getType();
        
        // Only track mining in the mines world
        if (!player.getWorld().getName().equals("minas")) {
            return;
        }
        
        // Track general block mining achievements
        long totalBlocksMined = BlocksMinedManager.getBlocksMined(player);
        
        // batismo_de_pedra - 500 blocks
        if (totalBlocksMined >= 500) {
            AchievementHelper.setProgress(player, "batismo_de_pedra", (int) totalBlocksMined);
        }
        
        // minerador_consistente - 25,000 blocks
        if (totalBlocksMined >= 25000) {
            AchievementHelper.setProgress(player, "minerador_consistente", (int) totalBlocksMined);
        }
        
        // minador_imparavel - 250,000 blocks
        if (totalBlocksMined >= 250000) {
            AchievementHelper.setProgress(player, "minador_imparavel", (int) totalBlocksMined);
        }
        
        // a_maquina - 1,000,000 blocks
        if (totalBlocksMined >= 1000000) {
            AchievementHelper.setProgress(player, "a_maquina", (int) totalBlocksMined);
        }
        
        // Track coal mining - maos_sujas
        if (blockType == Material.COAL_ORE) {
            int coalMined = coalBlocksMinedThisSession.getOrDefault(playerId, 0) + 1;
            coalBlocksMinedThisSession.put(playerId, coalMined);
            
            // Get total coal from existing progress + this session
            int currentCoalProgress = AchievementHelper.getProgress(player, "maos_sujas");
            AchievementHelper.setProgress(player, "maos_sujas", currentCoalProgress + 1);
        }
        
        // Track diamond mining - mao_de_diamante
        if (blockType == Material.DIAMOND_ORE) {
            int diamondsMined = diamondsCollectedThisSession.getOrDefault(playerId, 0) + 1;
            diamondsCollectedThisSession.put(playerId, diamondsMined);
            
            // Get total diamonds from existing progress + this session
            int currentDiamondProgress = AchievementHelper.getProgress(player, "mao_de_diamante");
            AchievementHelper.setProgress(player, "mao_de_diamante", currentDiamondProgress + 1);
        }
        
        // Track first mining for dia_de_pagamento (selling blocks)
        if (!hasMinedThisSession.getOrDefault(playerId, false)) {
            hasMinedThisSession.put(playerId, true);
            // This will be completed when they actually sell blocks - tracked elsewhere
        }
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onCraftItem(CraftItemEvent event) {
        if (event.isCancelled()) return;
        
        Player player = (Player) event.getWhoClicked();
        UUID playerId = player.getUniqueId();
        ItemStack result = event.getRecipe().getResult();
        
        // Track pickaxe crafting - primeira_picareta
        if (isPickaxe(result.getType())) {
            if (!hasCraftedPickaxeThisSession.getOrDefault(playerId, false)) {
                hasCraftedPickaxeThisSession.put(playerId, true);
                AchievementHelper.setProgress(player, "primeira_picareta", 1);
            }
        }
        
        // Track tool breaking - ferramenta_partida
        // This is handled when tools actually break, not when crafted
    }
    
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerDeath(PlayerDeathEvent event) {
        Player victim = event.getEntity();
        Player killer = victim.getKiller();
        
        // Track PvP achievements for killer
        if (killer != null && killer instanceof Player) {
            long totalKills = KillDeathManager.getKills(killer);
            
            // pvp_casual - first PvP win
            if (totalKills >= 1) {
                AchievementHelper.setProgress(killer, "pvp_casual", (int) totalKills);
            }
            
            // pvp_dominante - 50 PvP wins
            if (totalKills >= 50) {
                AchievementHelper.setProgress(killer, "pvp_dominante", (int) totalKills);
            }
            
            // pvp_sanguinario - 150 PvP kills
            if (totalKills >= 150) {
                AchievementHelper.setProgress(killer, "pvp_sanguinario", (int) totalKills);
            }
            
            // sombra_do_pvp - 500 PvP kills
            if (totalKills >= 500) {
                AchievementHelper.setProgress(killer, "sombra_do_pvp", (int) totalKills);
            }
        }
    }
    
    /**
     * Check if material is a pickaxe
     */
    private boolean isPickaxe(Material material) {
        switch (material) {
            case WOOD_PICKAXE:
            case STONE_PICKAXE:
            case IRON_PICKAXE:
            case GOLD_PICKAXE:
            case DIAMOND_PICKAXE:
                return true;
            default:
                return false;
        }
    }
    
    /**
     * Clean up session data when player leaves
     */
    public void cleanupPlayerSession(UUID playerId) {
        firstJoinThisSession.remove(playerId);
        hasMinedThisSession.remove(playerId);
        hasCraftedPickaxeThisSession.remove(playerId);
        coalBlocksMinedThisSession.remove(playerId);
        diamondsCollectedThisSession.remove(playerId);
    }
}
