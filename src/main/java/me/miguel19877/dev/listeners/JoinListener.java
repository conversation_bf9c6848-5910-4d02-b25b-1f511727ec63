package me.miguel19877.dev.listeners;

import me.miguel19877.dev.*;
import me.miguel19877.dev.events.PvPEvent;
import me.miguel19877.dev.managers.*;
import me.miguel19877.dev.listeners.AchievementClanListener;
import me.miguel19877.dev.utils.TitleAPI;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.entity.EntityExplodeEvent;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerRespawnEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.permissions.PermissionAttachment;
import org.bukkit.scheduler.BukkitRunnable;

public class JoinListener implements Listener {

    @EventHandler
    public void pvp(EntityDamageEvent e) {
        if (e.getEntity() instanceof Player) {
            if (e.getEntity().getWorld().getName().equals("minas")) {
                int x = e.getEntity().getLocation().getBlockX();
                int z = e.getEntity().getLocation().getBlockZ();
                e.setCancelled(!Rankup.minapvppvp.contains(x, 4, z));
            }else {
                e.setCancelled(true);
            }
        }
    }

    @EventHandler
    public void onKil(PlayerDeathEvent e) {
        new BukkitRunnable() {
            @Override
            public void run() {
                e.getEntity().spigot().respawn();
                // Optionally, clear velocity and fire ticks here
                // player.setVelocity(new Vector(0, 0, 0));
                // player.setFireTicks(0);
            }
        }.runTaskLater(Rankup.getInstance(), 5L); // Adjust delay as needed (e.g., 1 to 20 ticks)
        if (PvPEvent.getInstance(Rankup.getInstance()).isEventActive()) {
            Player victim = e.getEntity();
            PvPEvent.getInstance(Rankup.getInstance()).handlePlayerDeath(victim);
            e.getEntity().getInventory().clear();
            e.getDrops().clear();
        }
        //get inventory and drop it
        ItemStack[] items = e.getEntity().getInventory().getContents();
        ItemStack[] armor = e.getEntity().getInventory().getArmorContents();
        for (ItemStack item : items) {
            if (item != null && item.getType() != Material.AIR) {
                e.getEntity().getWorld().dropItemNaturally(e.getEntity().getLocation(), item);
            }
        }
        for (ItemStack item : armor) {
            if (item != null && item.getType() != Material.AIR) {
                e.getEntity().getWorld().dropItemNaturally(e.getEntity().getLocation(), item);
            }
        }

        //nova mensagem
        KillDeathManager.addDeaths(e.getEntity(), 1);
        if (e.getEntity().getKiller() != null) {
            Player killer = e.getEntity().getKiller();
            KillDeathManager.addKills(e.getEntity().getKiller(), 1);
            // Use default language for death messages since they're broadcast to all players
            e.setDeathMessage("§c" + e.getEntity().getName() + " §7foi morto por §c" + e.getEntity().getKiller().getName());
            if (ClanManager.getClanName(killer.getUniqueId()) != null) {
                Clan clan = ClanManager.getClan(ClanManager.getClanName(killer.getUniqueId()));
                ClanManager.addKill(clan);
            }
        } else {
            e.setDeathMessage("§c" + e.getEntity().getName() + " §7morreu.");
        }
    }

    @EventHandler
    public void onrespawn(PlayerRespawnEvent e) {
        if (PvPEvent.getInstance(Rankup.getInstance()).isEventActive()) {
            e.setRespawnLocation(PvPEvent.getInstance(Rankup.getInstance()).getLobbyLocation());
        }else {
            e.setRespawnLocation(new Location(Bukkit.getWorld("rankupspawn"), 818.5, 5.5, -83.5, 0, 0));
        }
    }

    @EventHandler
    public void tnt(EntityExplodeEvent event) {
        event.setCancelled(true);
    }

    @EventHandler
    public void onJoin(PlayerJoinEvent event) {
        Player p = event.getPlayer();

        String joinMessage = LanguageManager.getInstance().getMessage(p, "join.message", p.getName());
        event.setJoinMessage(joinMessage);
        //Set tablist header and footer seperately 1.8+
        //Organize tablist based on permissions

        givePermissions(p);
        PlayerDataListener.applyPlayerData(p);
        ClanManager.loadPlayer(p);

        new BukkitRunnable() {
            @Override
            public void run() {
                p.teleport(new Location(Bukkit.getWorld("rankupspawn"), 818.5, 5.5, -83.5, 0, 0));
                Rankup.getInstance().npc.show(p);
                Rankup.getInstance().topKillsNPC.show(p);
                Rankup.getInstance().topClanKillsNPC.show(p);
                String header = LanguageManager.getInstance().getMessage(p, "join.tab_header");
                String footer = LanguageManager.getInstance().getMessage(p, "join.tab_footer");
                TitleAPI.sendTabTitle(p, header, footer);
            }
        }.runTaskLater(Rankup.getInstance(), 8);
    }


    private void givePermissions(Player p) {
        PermissionAttachment attachment = p.addAttachment(Rankup.getInstance());
        attachment.setPermission("plots.confirm", true);
        attachment.setPermission("plots.list", true);
        attachment.setPermission("plots.home", true);
        attachment.setPermission("plots.visit", true);
        attachment.setPermission("plots.visit.other", true);
        attachment.setPermission("plots.visit.owned", true);
        attachment.setPermission("plots.visit.shared", true);
        attachment.setPermission("plots.visit.untrusted", true);
        attachment.setPermission("plots.claim", true);
        attachment.setPermission("plots.plot.1", true);
        attachment.setPermission("plots.auto", true);
        attachment.setPermission("plots.add", true);
        attachment.setPermission("plots.trust", true);
        attachment.setPermission("plots.remove", true);
        attachment.setPermission("plots.deny", true);
        attachment.setPermission("plots.grant", true);
        attachment.setPermission("plots.grant.add", true);
        attachment.setPermission("plots.grant.check", true);
        attachment.setPermission("plots.kick", true);
        attachment.setPermission("plots.set.home", true);
        attachment.setPermission("plots.use", true);
    }


}
