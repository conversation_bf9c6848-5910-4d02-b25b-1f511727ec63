package me.miguel19877.dev.listeners;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import me.miguel19877.dev.managers.AchievementManager;
import me.miguel19877.dev.utils.Achievement;
import me.miguel19877.dev.utils.AchievementPlayerData;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.plugin.messaging.PluginMessageListener;

import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.UUID;

/**
 * Handles plugin messages from Velocity for the achievement system
 */
public class AchievementMessageListener implements PluginMessageListener {
    
    private final Gson gson = new Gson();
    private final AchievementManager achievementManager = AchievementManager.getInstance();
    
    @Override
    public void onPluginMessageReceived(String channel, Player player, byte[] message) {
        if (!channel.equals(AchievementManager.ACHIEVEMENT_CHANNEL)) {
            return;
        }
        
        try {
            String json = new String(message, StandardCharsets.UTF_8);
            Map<String, Object> data = gson.fromJson(json, Map.class);
            
            String subChannel = (String) data.get("subChannel");
            if (subChannel == null) {
                Bukkit.getLogger().warning("Received achievement message without subChannel");
                return;
            }
            
            switch (subChannel) {
                case "INIT":
                    handleInitMessage(data);
                    break;
                default:
                    Bukkit.getLogger().warning("Unknown achievement subChannel: " + subChannel);
                    break;
            }
            
        } catch (Exception e) {
            Bukkit.getLogger().severe("Error processing achievement message: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Handle INIT message from Velocity containing achievement definitions and player data
     */
    private void handleInitMessage(Map<String, Object> data) {
        try {
            // Extract player ID
            String playerIdStr = (String) data.get("playerId");
            if (playerIdStr == null) {
                Bukkit.getLogger().warning("INIT message missing playerId");
                return;
            }
            
            UUID playerId = UUID.fromString(playerIdStr);
            
            // Extract achievement definitions
            Map<String, Object> achievementsData = (Map<String, Object>) data.get("achievements");
            Map<String, Achievement> achievements = parseAchievements(achievementsData);
            
            // Extract player data
            Map<String, Object> playerDataMap = (Map<String, Object>) data.get("playerData");
            AchievementPlayerData playerData = parsePlayerData(playerDataMap);
            
            // Initialize in achievement manager
            achievementManager.initializePlayerData(playerId, achievements, playerData);
            
            Bukkit.getLogger().info("Processed INIT message for player " + playerId + 
                    " with " + achievements.size() + " achievements");
            
        } catch (Exception e) {
            Bukkit.getLogger().severe("Error handling INIT message: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Parse achievement definitions from JSON data
     */
    private Map<String, Achievement> parseAchievements(Map<String, Object> achievementsData) {
        if (achievementsData == null) {
            Bukkit.getLogger().warning("No achievements data in INIT message");
            return new java.util.HashMap<>();
        }
        
        // Convert to proper Achievement objects
        Type achievementMapType = new TypeToken<Map<String, Achievement>>(){}.getType();
        String achievementsJson = gson.toJson(achievementsData);
        return gson.fromJson(achievementsJson, achievementMapType);
    }
    
    /**
     * Parse player achievement data from JSON data
     */
    private AchievementPlayerData parsePlayerData(Map<String, Object> playerDataMap) {
        if (playerDataMap == null) {
            Bukkit.getLogger().warning("No player data in INIT message");
            return new AchievementPlayerData();
        }
        
        try {
            // Extract player ID
            String playerIdStr = (String) playerDataMap.get("playerId");
            UUID playerId = playerIdStr != null ? UUID.fromString(playerIdStr) : null;
            
            // Extract progress map
            Map<String, Object> progressData = (Map<String, Object>) playerDataMap.get("progress");
            Map<String, Integer> progress = parseProgressMap(progressData);
            
            // Extract completions map
            Map<String, Object> completionsData = (Map<String, Object>) playerDataMap.get("completions");
            Map<String, Long> completions = parseCompletionsMap(completionsData);
            
            return new AchievementPlayerData(playerId, progress, completions);
            
        } catch (Exception e) {
            Bukkit.getLogger().severe("Error parsing player data: " + e.getMessage());
            e.printStackTrace();
            return new AchievementPlayerData();
        }
    }
    
    /**
     * Parse progress map, handling potential type conversion issues
     */
    private Map<String, Integer> parseProgressMap(Map<String, Object> progressData) {
        Map<String, Integer> progress = new java.util.HashMap<>();
        if (progressData != null) {
            for (Map.Entry<String, Object> entry : progressData.entrySet()) {
                Object value = entry.getValue();
                if (value instanceof Number) {
                    progress.put(entry.getKey(), ((Number) value).intValue());
                }
            }
        }
        return progress;
    }
    
    /**
     * Parse completions map, handling potential type conversion issues
     */
    private Map<String, Long> parseCompletionsMap(Map<String, Object> completionsData) {
        Map<String, Long> completions = new java.util.HashMap<>();
        if (completionsData != null) {
            for (Map.Entry<String, Object> entry : completionsData.entrySet()) {
                Object value = entry.getValue();
                if (value instanceof Number) {
                    completions.put(entry.getKey(), ((Number) value).longValue());
                }
            }
        }
        return completions;
    }
}
