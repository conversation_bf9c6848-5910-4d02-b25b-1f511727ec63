# Achievement System Implementation

## Overview
This document describes the achievement system implementation for the Spigot server that integrates with the Velocity proxy.

## Architecture

### Core Components

1. **Achievement Data Classes**
   - `Achievement.java` - Represents achievement definitions
   - `AchievementPlayerData.java` - Stores player progress and completions

2. **Manager Classes**
   - `AchievementManager.java` - Main system manager (Singleton)
   - Handles caching, progress tracking, and Velocity communication

3. **Listeners**
   - `AchievementMessageListener.java` - Handles plugin messages from Velocity
   - Processes INIT messages with achievement definitions and player data

4. **Commands**
   - `Achievements.java` - `/achievements` command to view progress
   - Aliases: `/ach`, `/conquistas`

5. **Utilities**
   - `AchievementHelper.java` - Convenience methods for progress tracking

## Plugin Messaging Protocol

### Channel: `"ach:main"`

### Message Types:

#### INIT Message (Velocity → Spigot)
```json
{
  "subChannel": "INIT",
  "playerId": "uuid-string",
  "achievements": {
    "achievement_id": {
      "id": "achievement_id",
      "name": "Achievement Name",
      "description": ["Description line 1", "Description line 2"],
      "icon": "MATERIAL_NAME",
      "threshold": 100,
      "category": "bronze",
      "commands": ["money add %player% 500", "give %player% diamond 1"]
    }
  },
  "playerData": {
    "playerId": "uuid-string",
    "progress": {
      "achievement_id": 25
    },
    "completions": {
      "completed_achievement_id": 1640995200000
    }
  }
}
```

#### UPDATE Message (Spigot → Velocity)
```json
{
  "playerId": "uuid-string",
  "achievementId": "achievement_id",
  "progress": 26
}
```

## Integration Points

### Main Plugin Class (Rankup.java)
- Registers `ach:main` plugin messaging channel
- Registers `AchievementMessageListener`
- Registers `/achievements` command

### Player Lifecycle
- **Join**: Achievement data loaded via INIT message
- **Quit**: Achievement cache cleaned up

### Language Support
Added language keys to both `messages_pt.yml` and `messages_en.yml`:
- `achievement.completed` - Achievement completion message
- `achievement.not_loaded` - Data not loaded message
- `achievement.none_available` - No achievements message
- `achievement.list_header` - Achievement list header
- `achievement.list_footer` - Achievement list footer

## Usage Examples

### For Achievement Tracking (Future Implementation)
```java
// In event listeners, increment progress
AchievementHelper.incrementProgress(player, "blocks_mined");
AchievementHelper.incrementProgress(player, "monster_kills", 1);

// Set specific progress
AchievementHelper.setProgress(player, "playtime_hours", 5);

// Check completion
if (AchievementHelper.hasCompleted(player, "first_kill")) {
    // Do something special
}
```

### Player Commands
- `/achievements` - View all achievements with progress
- `/ach` - Short alias
- `/conquistas` - Portuguese alias

## Data Flow

1. **Player joins server** → Velocity sends INIT message with achievements + progress
2. **AchievementMessageListener** processes INIT → Stores data in AchievementManager
3. **Player performs action** → Event listener calls AchievementHelper.incrementProgress()
4. **AchievementManager** checks for completion → Executes rewards if completed
5. **Progress update** sent to Velocity via plugin messaging
6. **Player leaves** → Achievement cache cleaned up

## Achievement Completion Handling

When an achievement is completed:
1. Completion message shown to player (localized)
2. Reward commands executed on main thread
3. Completion time recorded
4. Progress update sent to Velocity

## Categories

Achievements are organized by categories:
- `bronze` - Bronze achievements
- `silver` - Silver achievements  
- `gold` - Gold achievements
- `diamond` - Diamond achievements
- `special` - Special achievements
- `general` - General achievements (default)

## Thread Safety

- Uses `ConcurrentHashMap` for thread-safe caching
- Reward commands executed on main thread via scheduler
- Plugin messaging handled asynchronously

## Error Handling

- Graceful handling of missing achievement data
- Fallback messages when translations missing
- Logging for debugging achievement system issues
- Safe handling of malformed JSON messages

## Next Steps

1. **Add Event Listeners** for specific achievement types:
   - Block mining achievements
   - Monster killing achievements
   - PvP achievements
   - Playtime achievements
   - First-time achievements

2. **Achievement Definitions** will be provided by you to implement specific tracking

3. **Testing** with actual Velocity integration

## Files Modified/Created

### Created:
- `src/main/java/me/miguel19877/dev/utils/Achievement.java`
- `src/main/java/me/miguel19877/dev/utils/AchievementPlayerData.java`
- `src/main/java/me/miguel19877/dev/managers/AchievementManager.java`
- `src/main/java/me/miguel19877/dev/listeners/AchievementMessageListener.java`
- `src/main/java/me/miguel19877/dev/commands/Achievements.java`
- `src/main/java/me/miguel19877/dev/utils/AchievementHelper.java`

### Modified:
- `src/main/java/me/miguel19877/dev/Rankup.java` - Added channel registration and command
- `src/main/java/me/miguel19877/dev/listeners/QuitListener.java` - Added cleanup
- `src/main/java/me/miguel19877/dev/utils/PlayerDeltaManager.java` - Added achievement method
- `src/main/resources/plugin.yml` - Added achievements command
- `src/main/resources/messages_en.yml` - Added achievement messages
- `src/main/resources/messages_pt.yml` - Added achievement messages

The system is now ready for achievement tracking implementation!
